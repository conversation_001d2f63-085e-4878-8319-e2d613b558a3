// 心理健康筛查测评产品表头
export const COLUMNS = [
    {
        label: '维度名称',
        field: 'dimensionName',
        width: 150,
        align: 'center',
        fixed: 'left',
    },
    {
        label: '原始得分',
        field: 'scoreList',
        align: 'center',
        children: [],
    },
];
// 五维性格测评表头
export const CHARACTER_COLUMNS = [
    {
        label: '行动力',
        field: 1,
        align: 'center',
    },
    {
        label: '进取心',
        field: 2,
        align: 'center',
    },
    {
        label: '创造力',
        field: 3,
        align: 'center',
    },
    {
        label: '交际能力',
        field: 4,
        align: 'center',
    },
    {
        label: '说服力',
        field: 5,
        align: 'center',
    },
];
// hots测评产品等级划分
export const GRADE_LEVEL = [
    {
        label: '',
        value: 'minValue',
        labelWidth: '68px',
        disabled: true,
    },
    {
        label: '≤ 低 <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ 中 <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ 较高 <',
        value: 'minValue',
        labelWidth: '58px',
    },
    {
        label: '≤ 高 ≤',
        value: 'minValue',
        labelWidth: '48px',
        disabled: true,
    },
];
// disc测评产品等级划分
export const DISC_GRADE_LEVEL = [
    {
        label: '',
        value: 'minValue',
        labelWidth: '68px',
        disabled: true,
    },
    {
        label: '≤ 低 ≤',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '< 中 <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ 高 ≤',
        value: 'minValue',
        labelWidth: '48px',
        disabled: true,
    },
];
// hots测评产品等级划分
export const CAA_GRADE_LEVEL = [
    {
        label: 'E <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ D <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ C <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ B <',
        value: 'minValue',
        labelWidth: '58px',
    },
    {
        label: '≤ A',
        value: 'minValue',
        labelWidth: '28px',
        hide: true,
    },
];
// gba测评产品等级划分
export const GBA_GRADE_LEVEL = [
    {
        label: '',
        value: 'minValue',
        labelWidth: '68px',
        disabled: true,
    },
    {
        label: '≤ 低 <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ 中 <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ 高 ≤',
        value: 'minValue',
        labelWidth: '48px',
        disabled: true,
    },
];
// 职业动机测评产品等级划分（五维测评-潜在素质也在复用）
export const MA_GRADE_LEVEL = [
    {
        label: '低 <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ 较低 <',
        value: 'minValue',
        labelWidth: '60px',
    },
    {
        label: '≤ 中 <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ 较高 <',
        value: 'minValue',
        labelWidth: '60px',
    },
    {
        label: '≤ 高',
        value: 'minValue',
        labelWidth: '30px',
        hide: true,
    },
];
// 政治素养测评产品等级划分
export const PQA_GRADE_LEVEL = [
    {
        label: '一般 <',
        value: 'minValue',
        labelWidth: '48px',
    },
    {
        label: '≤ 良好 <',
        value: 'minValue',
        labelWidth: '60px',
    },
    {
        label: '≤ 优秀',
        value: 'minValue',
        labelWidth: '48px',
        hide: true,
    },
];
// 维度等级
export const DIMENSION_LEVELS = ['0', '1', '2', '3', '4'];
// 岗位匹配度分值等级
export const JOB_DIMENSION_LEVELS = [
    {
        label: '低',
        value: 'minValue',
    },
    {
        label: '中',
        value: 'maxValue',
    },
    {
        label: '高',
    },
];
// 基参数初始化参数配置
export const INIT_FORM_DATA = {
    filed_0: undefined,
    filed_1: undefined,
    filed_2: undefined,
    filed_3: undefined,
};
// 金融人才胜任力测评产品总分参数配置
export const FINANCE_PERSONNEL_COUNT_PARAMS = [
    {
        field: 'srlMin',
        label: 'SRL_MIN',
    },
    {
        field: 'srlGap',
        label: 'SRL_GAP',
    },
    {
        field: 'srlXs',
        label: 'SRL_XS',
    },
    {
        field: 'srlJs',
        label: 'SRL_JS',
    },
    {
        field: 'scoreMin',
        label: '得分下限',
    },
    {
        field: 'scoreMax',
        label: '得分上限',
    },
];
// 金融人才胜任力测评产品、领导力情景测评、16T职业性格产品计分规则导入表头
export const EXPORT_FILE_COLUMNS = [
    {
        label: '文件名称',
        field: 'fileName',
    },
    {
        label: '操作',
        field: 'operation',
        width: 140,
    },
];
// 16T职业性格产品-岗位列表表头
export const JOB_LIST_COLUMNS = [
    {
        label: '岗位',
        field: 'jobName',
    },
    {
        label: '状态',
        field: 'status',
    },
    {
        label: '操作',
        field: 'operation',
        width: 140,
    },
];
// 职业动机测评产品-职业锚列表表头
export const PROFESSIONAL_ANCHOR_LIST_COLUMNS = [
    {
        label: '职业锚',
        field: 'jobName',
    },
    {
        label: '操作',
        field: 'operation',
        width: 100,
    },
];
export const ENABLE_STATUS_CODE = 0;
export const DISABLED_STATUS_CODE = 1;
export const STATUS_MAP = {
    [ENABLE_STATUS_CODE]: '启用',
    [DISABLED_STATUS_CODE]: '停用',
};
// 顺序就是列表展示顺序
// 领导力情景测评产品文件名
export const FILE_NAME_MAP = {
    product5ManageActive: '管理行为',
    product5NormalParam: '常模参数',
    product5Level: '等级划分',
    product5LeaderStyle: '领导风格',
    product5Frequency: '频率表',
    product5Limit: '维度的超越百分比上下限',
};
// 心理健康测评团队版表头code(顺序固定)
export const MENTAL_HEALTH_TEAM_COLUMNS = ['product9TeamNormalParam', 'product9TeamMinMax', 'product9TeamIndexLevel', 'product9TeamQuestionWorkSatisfaction'];
// 心理健康测评团队版表头code(通用、顺序固定)
export const MENTAL_HEALTH_TEAM_COMMON_COLUMNS = ['product9DimensionCoreTest', 'product9DimensionAllScore', 'product9DimensionReportType'];
// 心理健康测评个人版表头code(顺序固定)
export const MENTAL_HEALTH_TEAM_PRIVATE_COLUMNS = [
    'product9PersonNormalParam',
    'product9PersonMinMax',
    'product9PersonIndexLevel',
    'product9PersonFrequency',
    'product9PersonPercentageLimit',
];
// 心理健康测评招聘版表头code(顺序固定)
export const MENTAL_HEALTH_TEAM_RECRUIT_COLUMNS = [
    'product9RecruitNormalParam',
    'product9RecruitMinMax',
    'product9RecruitIndexLevel',
    'product9RecruitFrequency',
    'product9RecruitPercentageLimit',
];
// HOTS产品总分表头code(顺序固定)
export const HOTS_TOTAL_SCORE_COLUMNS = ['product10BeyondPercent'];
// HOTS产品表头code(顺序固定)
export const HOTS_SUBJECT_SCORE_COLUMNS = [
    'product10Question1Scheme',
    'product10Question2Scheme',
    'product10Question3Scheme',
    'product10Question4Scheme',
    'product10Question5Scheme',
    'product10Question6Scheme',
    'product10Question7Scheme',
    'product10Question8Scheme',
    'product10Question9Scheme',
    'product10Question10Scheme',
];
// HPIO产品总分表头code(顺序固定)
export const HIPO_SCORE_COLUMNS = ['product12BeyondPercent'];
// HPIO产品总分表头code(顺序固定)
export const GBA_SCORE_COLUMNS = ['product13ExponentialNormParameter', 'product13ExponentialFrequency', 'product13BeyondPercentLowUp'];
export const GBA_GAME_SCORE_COLUMNS = ['product13QuestionFruitCompare', 'product13QuestionDigMine', 'product13DirectionTask', 'product13AreaJudge', 'product13TakeExpress'];

export const HANDWRITE_PROFESSION_SCORE_COLUMNS = ['product15OriginalScoreNormalParam', 'product15LevelDivideCompare', 'product15StandardScoreBeyondPercent'];
export const professionStyleList = Array.from({ length: 32 }, (_, i) => i + 1);

// 五维性格测评2.0产品计分规则导入表头code
export const CHARACTER_2_SCORE_COLUMNS = ['product21PercentLevelScoreMatch'];
