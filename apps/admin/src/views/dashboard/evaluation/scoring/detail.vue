<template>
    <MentalHealthDetail v-if="productId === PCode.HMA" />
    <CharacterDetail v-else-if="productId === PCode.OEA" />
    <FinancePersonnelDetail v-else-if="productId === PCode.FCA" />
    <LeadershipSkillsDetail v-else-if="productId === PCode.LPA" />
    <TProfessional v-else-if="productId === PCode.PTA" />
    <CharacterWDetail v-else-if="productId === PCode.OEWA" />
    <MentalHealthTeamDetail v-else-if="productId === PCode.OMHA" />
    <Hots v-else-if="productId === PCode.HOTS" />
    <Disc v-else-if="productId === PCode.DISC" />
    <Hipo v-else-if="productId === PCode.HIPO" />
    <Gba v-else-if="productId === PCode.GBA" />
    <HandwriteProfession v-else-if="productId === PCode.GPA" />
    <Caa v-else-if="productId === PCode.CAA" />
    <MotivationAssessment v-else-if="productId === PCode.MA" />
    <Nta v-else-if="productId === PCode.NTA" />
    <PoliticalQuality v-else-if="productId === PCode.PQA" />
    <Character2Detail v-else-if="productId === PCode.OEWA2" />
    <div v-else class="b-pagetip b-pagetip-empty">
        <img class="b-pagetip-img" src="/node_modules/@boss/design/es/assets/data-empty.png" alt="" />
        <p class="b-pagetip-text">该测评产品不存在</p>
    </div>
</template>

<script setup lang="ts">
import { PCode } from '@crm/biz-exam-product';
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import TProfessional from './detail/16t-professional.vue';
import Caa from './detail/caa.vue';
import CharacterDetail from './detail/character-detail.vue';
import CharacterWDetail from './detail/character-w-detail.vue';
import Character2Detail from './detail/character-2-detail.vue';
import Disc from './detail/disc.vue';
import FinancePersonnelDetail from './detail/finance-personnel-detail.vue';
import Gba from './detail/gba.vue';
import HandwriteProfession from './detail/handwrite-profession.vue';
import Hipo from './detail/hipo.vue';
import Hots from './detail/hots.vue';
import LeadershipSkillsDetail from './detail/leadership-skills.vue';
import MentalHealthDetail from './detail/mental-health-detail.vue';
import MentalHealthTeamDetail from './detail/mental-health-team-detail.vue';
import MotivationAssessment from './detail/motivation-assessment.vue';
import Nta from './detail/nta.vue';
import PoliticalQuality from './detail/political-quality.vue';

const $route = useRoute();
const productId = computed(() => Number($route.query?.productId));
</script>

<style scoped lang="less">
.b-pagetip-empty {
    margin-top: 10%;
}
</style>
