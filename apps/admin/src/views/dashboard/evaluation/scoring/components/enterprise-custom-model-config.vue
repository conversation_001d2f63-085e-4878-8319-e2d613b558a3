<template>
    <div class="enterprise-custom-model-config">
        <div class="sub-title">
            <h4>企业定制模型</h4>
            <b-button type="primary" @click="showAddModelDialog = true">
                <template #icon>
                    <b-icon name="plus" />
                </template>
                新建模型
            </b-button>
        </div>

        <!-- 模型列表 -->
        <b-table v-model:tableData="modelList" :columns="modelColumns" border tableLayoutFixed :scroll="{ y: '400px' }">
            <template #td-projectName="{ raw }">
                {{ raw.projectName || '-' }}
            </template>

            <template #td-modelName="{ raw }">
                {{ raw.modelName || '-' }}
            </template>

            <template #td-status="{ raw }">
                <b-tag :color="raw.status === 1 ? 'green' : 'red'">
                    {{ raw.status === 1 ? '启用' : '停用' }}
                </b-tag>
            </template>

            <template #td-operation="{ raw, $index }">
                <b-space>
                    <b-action @click="editModel(raw, $index)">编辑</b-action>
                    <b-action @click="toggleModelStatus(raw, $index)">
                        {{ raw.status === 1 ? '停用' : '启用' }}
                    </b-action>
                    <b-action @click="deleteModel($index)">删除</b-action>
                </b-space>
            </template>
        </b-table>

        <!-- 新建/编辑模型弹窗 -->
        <b-modal v-model="showAddModelDialog" :title="editingModelIndex === -1 ? '新建模型' : '编辑模型'" width="800px" @confirm="saveModel" @cancel="cancelEditModel">
            <b-form ref="modelFormRef" :model="modelForm" labelAlign="left" :rowNum="1">
                <b-form-item label="所属项目" field="projectId" asteriskPosition="end" :rules="[{ required: true, message: '请选择所属项目' }]">
                    <b-select v-model="modelForm.projectId" placeholder="请选择项目" allowSearch>
                        <b-option v-for="project in availableProjects" :key="project.id" :label="project.name" :value="project.id" />
                    </b-select>
                </b-form-item>

                <b-form-item
                    label="模型名称"
                    field="modelName"
                    asteriskPosition="end"
                    :rules="[
                        { required: true, message: '请输入模型名称' },
                        { max: 50, message: '模型名称不能超过50个字符' },
                    ]"
                >
                    <b-input v-model="modelForm.modelName" placeholder="请输入模型名称，上限50字" />
                </b-form-item>
            </b-form>

            <!-- 关键潜在素质配置 -->
            <div class="model-config-section">
                <h5>关键潜在素质</h5>
                <b-tabs v-model="activeConfigTab">
                    <b-tab-pane label="权重配置" name="weight">
                        <div class="config-placeholder">权重配置功能开发中...</div>
                    </b-tab-pane>
                    <b-tab-pane label="常模配置" name="norm">
                        <div class="config-placeholder">常模配置功能开发中...</div>
                    </b-tab-pane>
                    <b-tab-pane label="定义配置" name="definition">
                        <div class="config-placeholder">定义配置功能开发中...</div>
                    </b-tab-pane>
                    <b-tab-pane label="等级语言" name="levelLanguage">
                        <div class="config-placeholder">等级语言配置功能开发中...</div>
                    </b-tab-pane>
                </b-tabs>
            </div>

            <!-- 岗位素质模型配置 -->
            <div class="model-config-section">
                <h5>岗位素质模型</h5>
                <b-tabs v-model="activeJobConfigTab">
                    <b-tab-pane label="权重配置" name="weight">
                        <div class="config-placeholder">权重配置功能开发中...</div>
                    </b-tab-pane>
                    <b-tab-pane label="常模配置" name="norm">
                        <div class="config-placeholder">常模配置功能开发中...</div>
                    </b-tab-pane>
                    <b-tab-pane label="岗位定义" name="definition">
                        <div class="config-placeholder">岗位定义配置功能开发中...</div>
                    </b-tab-pane>
                </b-tabs>
            </div>
        </b-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface ModelItem {
    id?: string;
    projectId: string;
    projectName: string;
    modelName: string;
    status: number; // 1: 启用, 0: 停用
    keyPotentialQuality: {
        weight: any;
        norm: any;
        definition: any;
        levelLanguage: any;
    };
    jobQualityModel: {
        weight: any;
        norm: any;
        definition: any;
    };
}

interface ProjectItem {
    id: string;
    name: string;
    hasCustomModel: boolean;
}

// 模型列表
const modelList = ref<ModelItem[]>([]);

// 可用项目列表（排除已关联定制模型的项目）
const availableProjects = ref<ProjectItem[]>([]);

// 弹窗控制
const showAddModelDialog = ref(false);
const editingModelIndex = ref(-1);

// 配置标签页
const activeConfigTab = ref('weight');
const activeJobConfigTab = ref('weight');

// 模型表单
const modelForm = ref<ModelItem>({
    projectId: '',
    projectName: '',
    modelName: '',
    status: 1,
    keyPotentialQuality: {
        weight: {},
        norm: {},
        definition: {},
        levelLanguage: {},
    },
    jobQualityModel: {
        weight: {},
        norm: {},
        definition: {},
    },
});

const modelFormRef = ref();

// 表格列配置
const modelColumns = [
    {
        label: '所属项目',
        field: 'projectName',
        width: 200,
    },
    {
        label: '模型名称',
        field: 'modelName',
        width: 200,
    },
    {
        label: '状态',
        field: 'status',
        width: 100,
    },
    {
        label: '操作',
        field: 'operation',
        width: 200,
    },
];

// 新建模型
function resetModelForm() {
    modelForm.value = {
        projectId: '',
        projectName: '',
        modelName: '',
        status: 1,
        keyPotentialQuality: {
            weight: {},
            norm: {},
            definition: {},
            levelLanguage: {},
        },
        jobQualityModel: {
            weight: {},
            norm: {},
            definition: {},
        },
    };
}

// 编辑模型
function editModel(model: ModelItem, index: number) {
    editingModelIndex.value = index;
    modelForm.value = { ...model };
    showAddModelDialog.value = true;
}

// 保存模型
async function saveModel() {
    const valid = await modelFormRef.value.validate();
    if (!valid) return;

    try {
        if (editingModelIndex.value === -1) {
            // 新建
            const newModel = { ...modelForm.value };
            newModel.id = Date.now().toString();
            newModel.projectName = availableProjects.value.find((p) => p.id === newModel.projectId)?.name || '';
            modelList.value.push(newModel);
        } else {
            // 编辑
            modelList.value[editingModelIndex.value] = { ...modelForm.value };
        }

        showAddModelDialog.value = false;
        editingModelIndex.value = -1;
        resetModelForm();

        Toast.success('保存成功');
    } catch (error) {
        Toast.danger('保存失败');
    }
}

// 取消编辑
function cancelEditModel() {
    showAddModelDialog.value = false;
    editingModelIndex.value = -1;
    resetModelForm();
}

// 切换模型状态
function toggleModelStatus(model: ModelItem, index: number) {
    modelList.value[index].status = model.status === 1 ? 0 : 1;
    Toast.success('状态更新成功');
}

// 删除模型
function deleteModel(index: number) {
    Dialog.open({
        type: 'warning',
        title: '删除确认',
        content: '确定要删除这个模型吗？',
        confirm() {
            modelList.value.splice(index, 1);
            Toast.success('删除成功');
        },
    });
}

// 加载项目列表
async function loadProjects() {
    // 这里应该调用API获取项目列表
    // 暂时使用模拟数据
    availableProjects.value = [
        { id: '1', name: '项目A', hasCustomModel: false },
        { id: '2', name: '项目B', hasCustomModel: false },
        { id: '3', name: '项目C', hasCustomModel: false },
    ];
}

onMounted(() => {
    loadProjects();
});
</script>

<style lang="less" scoped>
.enterprise-custom-model-config {
    padding: 20px 0;
}

.sub-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.model-config-section {
    margin: 20px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0 0 10px 0;
    }
}

.config-placeholder {
    padding: 40px;
    text-align: center;
    color: #86909c;
    font-size: 14px;
}
</style>
