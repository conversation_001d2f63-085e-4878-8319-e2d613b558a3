<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <b-col :span="12">
            <b-form-item label="常模平均分" asteriskPosition="end" field="normalAverageScore" :rules="[{ type: 'number', required: true, message: '请填写常模平均分' }]">
                <b-input-number
                    v-model.trim="modelValue.normalAverageScore"
                    placeholder="计分使用，请填写0-1000内数值，最多4为小数"
                    hideButton
                    :min="0"
                    :max="1000"
                    :precision="4"
                    :formatter="scoreFormatter"
                />
            </b-form-item>
        </b-col>
        <b-col :span="12">
            <b-form-item label="常模标准差" required asteriskPosition="end" field="normalDeviation" :rules="[{ type: 'number', required: true, message: '请填写常模标准差' }]">
                <b-input-number
                    v-model.trim="modelValue.normalDeviation"
                    placeholder="请填写1000以内正数，最多4为小数"
                    hideButton
                    :min="0"
                    :max="1000"
                    :precision="4"
                    :formatter="scoreFormatter"
                />
            </b-form-item>
        </b-col>
        <b-col :span="12">
            <b-form-item label="建议作答时间" required asteriskPosition="end" field="adviseAnswerTime" :rules="[{ type: 'number', required: true, message: '请填写建议作答时间' }]">
                <b-input-number
                    v-model.trim="modelValue.adviseAnswerTime"
                    class="input-number-block-suffix"
                    placeholder="请填写30以内的数字，最多1位小数"
                    hideButton
                    :min="0.1"
                    :max="30"
                    :precision="1"
                >
                    <template #suffix> 分钟 </template>
                </b-input-number>
            </b-form-item>
            <div class="hint">
                <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                <span class="hint-text">考生答题时可见的提示时间</span>
            </div>
        </b-col>
        <b-col :span="12">
            <b-row :gutter="[20, 24]" class="answerT-time-group">
                <b-col class="min-valid-answer-time" :span="13">
                    <b-form-item
                        label="有效作答时间范围"
                        required
                        asteriskPosition="end"
                        field="minValidAnswerTime"
                        :rules="[
                            { type: 'number', required: true, message: '请填写有效作答时间范围' },
                            { validator: (value, callback) => formValidator(value, callback, 'minValidAnswerTime') },
                        ]"
                    >
                        <b-input-number
                            v-model.trim="modelValue.minValidAnswerTime"
                            class="input-number-block-suffix"
                            placeholder="请填写"
                            hideButton
                            :min="0.1"
                            :max="30"
                            :precision="1"
                            @blur="rangeBlur"
                        >
                            <template #suffix> 分钟 </template>
                        </b-input-number>
                        <div class="range-separator" />
                    </b-form-item>
                </b-col>
                <b-col :span="11" class="max-valid-answer-time">
                    <b-form-item
                        required
                        asteriskPosition="end"
                        field="maxValidAnswerTime"
                        :rules="[
                            { type: 'number', required: true, message: '请填写有效作答时间范围' },
                            { validator: (value: any, callback: any) => formValidator(value, callback, 'maxValidAnswerTime') },
                        ]"
                    >
                        <b-input-number
                            v-model.trim="modelValue.maxValidAnswerTime"
                            class="input-number-block-suffix"
                            placeholder="请填写"
                            hideButton
                            :min="0.1"
                            :max="30"
                            :precision="1"
                            @blur="rangeBlur"
                        >
                            <template #suffix> 分钟 </template>
                        </b-input-number>
                    </b-form-item>
                </b-col>
            </b-row>
            <div class="hint">
                <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                <span class="hint-text">报告参考性的判断条件</span>
            </div>
        </b-col>
        <b-col :span="24">
            <b-form-item label="适配人群" required asteriskPosition="end" field="crowd" :rules="[{ type: 'array', required: true, message: '请选择适配人群' }]">
                <b-select v-model="modelValue.crowd" multiple :options="CROWD_LIST" placeholder="请选择" />
            </b-form-item>
        </b-col>
        <b-col :span="24">
            <b-form-item label="维度说明" field="description" asteriskPosition="end">
                <b-textarea v-model.trim="modelValue.description" placeholder="请填写" showWordLimit :maxLength="100" :showResize="false" :autoSize="{ minRows: 2 }" />
            </b-form-item>
        </b-col>
    </b-row>
</template>

<script setup lang="ts">
import { scoreFormatter, timeFormatter } from '@/utils/index';
import { CROWD_LIST } from '../constant';

defineOptions({
    name: 'BaseInfo',
});

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({}),
    },
});

function rangeBlur() {
    const min = props.modelValue.minValidAnswerTime;
    const max = props.modelValue.maxValidAnswerTime;
    if (min && max) {
        props.modelValue.minValidAnswerTime = Math.min(min, max);
        props.modelValue.maxValidAnswerTime = Math.max(min, max);
    }
}
function formValidator(value, callback, field) {
    if (
        props.modelValue.minValidAnswerTime >= 0 &&
        props.modelValue.maxValidAnswerTime >= 0 &&
        ((field === 'maxValidAnswerTime' && value <= props.modelValue.adviseAnswerTime) || (field === 'minValidAnswerTime' && value >= props.modelValue.adviseAnswerTime))
    ) {
        callback('建议作答时间必须在有效作答时间范围内');
        return;
    }
    callback();
}
function resetFormData() {
    props.modelValue.normalAverageScore = undefined;
    props.modelValue.normalDeviation = undefined;
    props.modelValue.maxValidAnswerTime = undefined;
    props.modelValue.minValidAnswerTime = undefined;
    props.modelValue.adviseAnswerTime = undefined;
    props.modelValue.crowd = [];
    props.modelValue.descriptionOf16T = {
        1: '',
        2: '',
    };
}
defineExpose({ resetFormData });
</script>
