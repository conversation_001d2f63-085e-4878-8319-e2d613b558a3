<template>
    <b-section type="search">
        <b-form class="dimension-list-form" labelAlign="inner" :model="queryData" useGrid :gridProps="{ gap: 10 }" layout="inline" @submit="(e) => onSearch()">
            <b-form-item label="测评产品">
                <b-select v-model="queryData.productId" :options="productList" allowSearch placeholder="请选择" @change="getParentDimensionList" />
            </b-form-item>
            <b-form-item label="上级维度">
                <b-select v-model="queryData.parentDimensionId" :fieldNames="fieldNames" :options="parentDimensionList" allowSearch allowClear placeholder="请选择" />
            </b-form-item>
            <b-form-item label="维度名称">
                <b-input v-model.trim="queryData.dimensionName" trimBeforePaste placeholder="请输入" allowClear :maxLength="30" />
            </b-form-item>
            <b-form-item label="状态">
                <b-select v-model="queryData.dimensionStatus" placeholder="请选择">
                    <b-option v-for="(item, index) in DIMENSION_STATUS_LIST" :key="index" :label="item.label" :value="item.value" />
                </b-select>
            </b-form-item>
            <b-form-item :alwaysShow="true" :showMore="true">
                <b-space>
                    <b-button type="outline" status="primary" htmlType="submit"> 查询 </b-button>
                </b-space>
            </b-form-item>
        </b-form>
    </b-section>
    <TableList ref="listTable" title="测评维度" :getData="_getDimensionList" :query="tableQuery" :columns="TABLE_COLUMN">
        <template #table-title>
            <b-button type="primary" @click="() => edit()">
                <b-space gap="5px"> <SvgIcon name="hicon-add" width="16" height="16" /><span>新增维度</span> </b-space>
            </b-button>
        </template>
        <template #td-dimensionStatusDesc="{ raw }">
            <div class="cell-wrap">
                <div class="status-indicator" :class="[`status-indicator-${raw.dimensionStatus}`]" />
                <div class="status-text">
                    {{ raw.dimensionStatusDesc }}
                </div>
            </div>
        </template>
        <template #td-operation="{ raw }">
            <template v-if="!(raw.productId === PCode.PQA && raw.disabled === 0)">
                <!-- 工作风格测评产品和高潜人才识别测评产品、笔迹职业适应力测评产品一级维度不允许被修改和停用，按钮隐藏 -->
                <b-action @click="() => edit(raw.encryptId)"> 编辑 </b-action>
                <b-action v-if="raw.dimensionStatus === 0" @click="() => changeStatus(raw, CLOSE_DIMENSION_STATUS)"> 停用 </b-action>
                <b-action v-else-if="raw.dimensionStatus === 1" @click="() => changeStatus(raw, OPEN_DIMENSION_STATUS)"> 启用 </b-action>
            </template>
        </template>
    </TableList>
    <DialogDimensionDetail v-if="detailDialogShow" v-model="detailDialogShow" :encryptId="currentEditDimensionId" @success="() => onSearch(true)" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { _getDimensionList, _updateDimensionStatus, _validateDimensionUpdateParam } from '@/services/api/dimension';
import { _getProductOptions } from '@/services/api/project';
import { PCode } from '@crm/biz-exam-product';
import { createVNode, onMounted, reactive, ref } from 'vue';
import { CLOSE_DIMENSION_STATUS, DIMENSION_STATUS_LIST, OPEN_DIMENSION_STATUS, TABLE_COLUMN } from './constant';
import DialogDimensionDetail from './dialog-dimension-detail.vue';
import TableList from '@/components/table-list/index.vue';

defineOptions({
    name: 'DimensionManage',
});
const fieldNames = { value: 'id', label: 'name' };
const tipTextMap = {
    [CLOSE_DIMENSION_STATUS]: '停用成功',
    [OPEN_DIMENSION_STATUS]: '启用成功',
};

const baseQueryData = {
    productId: -1,
    dimensionName: '',
    dimensionStatus: 0,
    parentDimensionId: '',
};
const queryData = reactive(baseQueryData);
const listTable = ref<any>();

const tableQuery = computed(() => {
    const params: any = { ...queryData };
    for (const key in params) {
        if (Object.prototype.hasOwnProperty.call(params, key)) {
            if (params[key] === -1) {
                delete params[key];
            }
        }
    }
    return params;
});

const productList = ref([
    {
        label: '全部',
        value: -1,
    },
]);

/**
 * 获取上级维度下拉数据
 */
const parentDimensionList = ref([]);

const detailDialogShow = ref(false);
const currentEditDimensionId = ref();

function onSearch(isKeepPage?: boolean) {
    listTable.value.getTableList({ keepPage: !!isKeepPage });
}

function edit(encryptId?: string) {
    currentEditDimensionId.value = encryptId;
    detailDialogShow.value = true;
}

function contentDom(raw: { dimensionName: string }, action: number) {
    if (action === CLOSE_DIMENSION_STATUS) {
        return createVNode('div', null, [
            '确定停用',
            createVNode('span', { class: 'text-primary' }, raw.dimensionName),
            '维度吗？该维度下如有下属维度将全部同步停用，但已存在的项目和试卷数据不受影响',
        ]);
    }
    if (action === OPEN_DIMENSION_STATUS) {
        return createVNode('div', null, ['确定启用', createVNode('span', { class: 'text-primary' }, raw.dimensionName), '维度吗？该维度如有下属维度需要启用，需手动调整']);
    }
}

function confirmDialog(raw: any, action: 0 | 1) {
    Dialog.open({
        type: 'warning',
        title: '请确认',
        content: () => contentDom(raw, action),
        async confirm() {
            const updateRes = await _updateDimensionStatus({
                encryptId: raw.encryptId,
                status: action,
            });
            if (updateRes.code === 0) {
                Toast.success(tipTextMap[action]);
                onSearch(queryData.dimensionStatus === -1);
            }
        },
    });
}
async function changeStatus(raw: any, action: 0 | 1) {
    const checkRes = await _validateDimensionUpdateParam({
        encryptId: raw.encryptId,
        status: action,
    });
    if (checkRes.code === 0) {
        await confirmDialog(raw, action);
    } else {
        Toast.danger({
            wrapClass: 'toast-update-status-error',
            content: checkRes.message,
        });
    }
}

async function getParentDimensionList() {
    queryData.parentDimensionId = '';
    try {
        if (queryData.productId === -1) {
            parentDimensionList.value = [];
            return;
        }
        const { code, data } = await Invoke.dimension.parentDimension({ productId: queryData.productId });
        if (code === 0) {
            parentDimensionList.value = data || [];
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    }
}

async function getProductList() {
    const params = { source: 3 } as const;
    const res = await _getProductOptions(params);
    if (res.code === 0) {
        const list = res.data
            ?.map((x: any) => ({
                ...x,
                value: x.id,
                label: x.name,
            }))
            ?.filter((item: { value: number }) => ![PCode.HOTS, PCode.GBA].includes(item.value));
        productList.value = [
            {
                label: '全部',
                value: -1,
            },
            ...list,
        ];
    }
}

onMounted(() => {
    getProductList();
    onSearch();
});
</script>

<style lang="less">
.dimension-page {
    .add-dimension-icon {
        margin-right: 5px;
    }
    .dimension-list-form {
        .b-grid {
            grid-template-columns: repeat(auto-fill, minmax(270px, 1fr)) !important;
        }
    }
}
.b-toast.toast-update-status-error.b-toast-danger {
    max-width: 50%;
    line-height: 1.5;
    align-items: flex-start;
    text-align: left;
    .b-toast-icon {
        line-height: 1;
    }
}
</style>
