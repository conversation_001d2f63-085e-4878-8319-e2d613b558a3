<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <b-col :span="24">
            <b-form-item label="上级维度" asteriskPosition="end" class="parentId">
                <b-select
                    v-model="modelValue.encryptParentId"
                    :options="parentDimensionList"
                    :fieldNames="fieldNames"
                    placeholder="请选择"
                    :disabled="isEdit"
                    allowClear
                    allowSearch
                    :fallbackOption="fallback"
                    @change="changeParentProduct"
                />
            </b-form-item>
        </b-col>
    </b-row>
</template>

<script setup lang="ts">
import { _dimensionOption } from '@/services/api/dimension';
import { ref, watchEffect } from 'vue';
import { PCode } from '@crm/biz-exam-product';

defineOptions({
    name: 'LeadershipSkills',
});
const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({}),
    },
    isEdit: {
        type: Boolean,
        default: false,
    },
    productId: {
        type: String,
        default: PCode.LPA,
    },
});
const emit = defineEmits(['changeProduct', 'updateParentData']);
const fieldNames = { value: 'encryptId', label: 'name' };
function fallback(value: string) {
    return {
        value,
        label: '请选择',
    };
}
function changeParentProduct(value?: string) {
    if (!value) {
        resetFormData();
    }
}
function resetFormData() {
    props.modelValue.factorType = undefined;
    props.modelValue.normalAverageScore = undefined;
    props.modelValue.normalDeviation = undefined;
    props.modelValue.maxValidAnswerTime = undefined;
    props.modelValue.minValidAnswerTime = undefined;
    props.modelValue.adviseAnswerTime = undefined;
    props.modelValue.crowd = [];
    props.modelValue.encryptParentId = undefined;
    props.modelValue.description = undefined;
    props.modelValue.descriptionOf16T = {
        1: '',
        2: '',
    };
}
const parentDimensionList = ref([]);
async function getEvaluationDimensionOption() {
    try {
        const params = {
            dimensionStatus: 0, // 0-启用;1-停用
            productId: props.productId,
            dimensionLevel: 1,
        };
        const { code, data } = await _dimensionOption(params);
        if (code === 0) {
            parentDimensionList.value = data || [];
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    }
}
watchEffect(() => {
    getEvaluationDimensionOption();
});
emit('updateParentData');
defineExpose({ resetFormData });
</script>

<style scoped lang="less">
.parentId {
    :deep(.b-form-item-content) {
        max-width: 50%;
    }
}
</style>
