<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <b-col :span="12">
            <b-form-item label="常模平均分" asteriskPosition="end" field="normalAverageScore" :rules="[{ type: 'number', required: true, message: '请填写常模平均分' }]">
                <b-input-number
                    v-model.trim="modelValue.normalAverageScore"
                    placeholder="计分使用，请填写0-1000内数值，最多4为小数"
                    hideButton
                    :min="0"
                    :max="1000"
                    :precision="4"
                    :formatter="scoreFormatter"
                />
            </b-form-item>
        </b-col>
        <b-col :span="12">
            <b-form-item label="常模标准差" required asteriskPosition="end" field="normalDeviation" :rules="[{ type: 'number', required: true, message: '请填写常模标准差' }]">
                <b-input-number
                    v-model.trim="modelValue.normalDeviation"
                    placeholder="请填写1000以内正数，最多4为小数"
                    hideButton
                    :min="0"
                    :max="1000"
                    :precision="4"
                    :formatter="scoreFormatter"
                />
            </b-form-item>
        </b-col>
        <b-col :span="24">
            <b-form-item
                label="维度说明-岗位匹配版"
                field="descriptionOf16T.1"
                asteriskPosition="end"
                :rules="[{ type: 'string', required: true, message: '请填写维度说明-岗位匹配版' }]"
            >
                <b-textarea v-model.trim="modelValue.descriptionOf16T[1]" placeholder="请填写" showWordLimit :maxLength="25" :showResize="false" :autoSize="{ minRows: 2 }" />
            </b-form-item>
        </b-col>
        <b-col :span="24">
            <b-form-item label="维度说明-通用版" field="descriptionOf16T.2" asteriskPosition="end" :rules="[{ type: 'string', required: true, message: '请填写维度说明-通用版' }]">
                <b-input v-model.trim="modelValue.descriptionOf16T[2]" placeholder="请输入" showWordLimit :maxLength="5" :showResize="false" :autoSize="{ minRows: 2 }" />
            </b-form-item>
        </b-col>
        <b-col :span="12">
            <b-form-item
                label="维度倾向组"
                tooltip="四个维度倾向组代表：注意力方向（外倾、内倾）；认知方式（实感、直觉）；决策方式（理智、情感）；生活方式（判断、理解）"
                required
                asteriskPosition="end"
                field="dimensionTendency"
                :rules="[{ type: 'number', required: true, message: '请选择维度倾向组' }]"
            >
                <b-select v-model="modelValue.dimensionTendency" placeholder="请选择" :disabled="isEdit" @change="handleChangeDimensionTendency">
                    <b-option v-for="item in DIMENSIONAL_TENDENCY_GROUP_LIST" :key="item.value" :value="item.value">
                        {{ item.label }}
                    </b-option>
                </b-select>
            </b-form-item>
        </b-col>
        <b-col :span="24">
            <b-table :tableData="modelValue.dimensionTendencyConfig" :columns="DIMENSIONAL_TENDENCY_GROUP_COLUMNS">
                <template #th-tendencyName> 倾向名称<span style="color: var(--danger-color-6); margin-left: 4px">*</span> </template>
                <template #th-tendencyDesc> 描述<span style="color: var(--danger-color-6); margin-left: 4px">*</span> </template>
                <template #td-tendency="{ raw }">
                    {{ raw.tendency === 'left' ? '左倾向' : '右倾向' }}
                </template>
                <template #td-tendencyName="{ raw }">
                    <b-input v-model.trim="raw.tendencyName" placeholder="请输入" :maxLength="5" showWordLimit />
                </template>
                <template #td-tendencyDesc="{ raw }">
                    <b-input v-model.trim="raw.tendencyDesc" placeholder="请输入" :maxLength="10" showWordLimit />
                </template>
            </b-table>
        </b-col>
    </b-row>
</template>

<script setup lang="ts">
import { scoreFormatter } from '@/utils/index';
import { DIMENSIONAL_TENDENCY_GROUP_COLUMNS, DIMENSIONAL_TENDENCY_GROUP_LIST } from '../constant';

defineOptions({
    name: 'TProfessional',
});

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({}),
    },
    isEdit: {
        type: Boolean,
        default: false,
    },
});

function handleChangeDimensionTendency(value) {
    const tendencySymbolArray = value?.split('-') || [];
    props.modelValue.dimensionTendencyConfig[0].tendencySymbol = tendencySymbolArray[0];
    props.modelValue.dimensionTendencyConfig[1].tendencySymbol = tendencySymbolArray[1];
}
function resetFormData() {
    props.modelValue.normalAverageScore = undefined;
    props.modelValue.normalDeviation = undefined;
    props.modelValue.maxValidAnswerTime = undefined;
    props.modelValue.minValidAnswerTime = undefined;
    props.modelValue.adviseAnswerTime = undefined;
    props.modelValue.crowd = [];
    props.modelValue.dimensionTendency = undefined;
    props.modelValue.dimensionTendencyConfig = [];
    props.modelValue.priority = undefined;
}
if (!props.isEdit) {
    props.modelValue.dimensionTendencyConfig = [
        {
            tendency: 'left',
            tendencySymbol: '',
            tendencyName: '',
            tendencyDesc: '',
        },
        {
            tendency: 'right',
            tendencySymbol: '',
            tendencyName: '',
            tendencyDesc: '',
        },
    ];
}
defineExpose({ resetFormData });
</script>
