<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <b-col :span="12">
            <b-form-item label="常模平均分" asteriskPosition="end" field="normalAverageScore" :rules="[{ type: 'number', required: true, message: '请填写常模平均分' }]">
                <b-input-number
                    v-model.trim="modelValue.normalAverageScore"
                    placeholder="计分使用，请填写0-1000内数值，最多4为小数"
                    hideButton
                    :min="0"
                    :max="1000"
                    :precision="4"
                    :formatter="scoreFormatter"
                />
            </b-form-item>
        </b-col>
        <b-col :span="12">
            <b-form-item label="常模标准差" required asteriskPosition="end" field="normalDeviation" :rules="[{ type: 'number', required: true, message: '请填写常模标准差' }]">
                <b-input-number
                    v-model.trim="modelValue.normalDeviation"
                    placeholder="请填写1000以内正数，最多4为小数"
                    hideButton
                    :min="0"
                    :max="1000"
                    :precision="4"
                    :formatter="scoreFormatter"
                />
            </b-form-item>
        </b-col>
    </b-row>
</template>

<script setup lang="ts">
import { scoreFormatter } from '@/utils/index';

defineOptions({
    name: 'Hipo',
});

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({}),
    },
    isEdit: {
        type: Boolean,
        default: false,
    },
});
function resetFormData() {
    props.modelValue.factorType = undefined;
    props.modelValue.normalAverageScore = undefined;
    props.modelValue.normalDeviation = undefined;
    props.modelValue.maxValidAnswerTime = undefined;
    props.modelValue.minValidAnswerTime = undefined;
    props.modelValue.adviseAnswerTime = undefined;
    props.modelValue.crowd = [];
    props.modelValue.encryptParentId = undefined;
    props.modelValue.priority = undefined;
    props.modelValue.description = undefined;
    props.modelValue.descriptionOf16T = {
        1: '',
        2: '',
    };
}
defineExpose({ resetFormData });
</script>

<style scoped lang="less">
.parentId {
    :deep(.b-form-item-content) {
        max-width: 50%;
    }
}
</style>
