<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <b-col :span="24">
            <b-form-item label="上级维度" asteriskPosition="end" field="encryptParentId" class="parentId" :rules="[{ type: 'string', required: true, message: '请选择上级维度' }]">
                <b-select
                    v-model="modelValue.encryptParentId"
                    :options="parentDimensionList"
                    placeholder="请选择"
                    :disabled="isEdit"
                    allowClear
                    allowSearch
                    :fallbackOption="fallback"
                />
            </b-form-item>
        </b-col>
        <b-col :span="12">
            <b-form-item label="常模平均分" asteriskPosition="end" field="normalAverageScore" :rules="[{ type: 'number', required: true, message: '请填写常模平均分' }]">
                <b-input-number
                    v-model.trim="modelValue.normalAverageScore"
                    placeholder="计分使用，请填写0-1000内数值，最多4为小数"
                    hideButton
                    :min="0"
                    :max="1000"
                    :precision="4"
                    :formatter="scoreFormatter"
                />
            </b-form-item>
        </b-col>
        <b-col :span="12">
            <b-form-item label="常模标准差" required asteriskPosition="end" field="normalDeviation" :rules="[{ type: 'number', required: true, message: '请填写常模标准差' }]">
                <b-input-number
                    v-model.trim="modelValue.normalDeviation"
                    placeholder="请填写1000以内正数，最多4为小数"
                    hideButton
                    :min="0"
                    :max="1000"
                    :precision="4"
                    :formatter="scoreFormatter"
                />
            </b-form-item>
        </b-col>
    </b-row>
</template>

<script setup lang="ts">
import { _dimensionOption } from '@/services/api/dimension';
import { scoreFormatter } from '@/utils/index';
import { ref } from 'vue';
import { PCode } from '@crm/biz-exam-product';

defineOptions({
    name: 'Hipo',
});

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({}),
    },
    isEdit: {
        type: Boolean,
        default: false,
    },
    productId: {
        type: String,
        default: PCode.HIPO,
    },
});

function fallback(value) {
    return {
        value,
        label: '请选择',
    };
}
function resetFormData() {
    props.modelValue.factorType = undefined;
    props.modelValue.normalAverageScore = undefined;
    props.modelValue.normalDeviation = undefined;
    props.modelValue.maxValidAnswerTime = undefined;
    props.modelValue.minValidAnswerTime = undefined;
    props.modelValue.adviseAnswerTime = undefined;
    props.modelValue.crowd = [];
    props.modelValue.encryptParentId = undefined;
    props.modelValue.priority = undefined;
    props.modelValue.description = undefined;
    props.modelValue.descriptionOf16T = {
        1: '',
        2: '',
    };
}
const parentDimensionList = ref([]);
async function getEvaluationDimensionOption() {
    try {
        const params = {
            dimensionStatus: 0, // 0-启用;1-停用
            productId: props.productId,
            dimensionLevel: 1,
        };
        const { code, data } = await _dimensionOption(params);
        if (code === 0) {
            parentDimensionList.value =
                data?.map((x: any) => ({
                    ...x,
                    value: x.encryptId,
                    label: x.name,
                })) || [];
        }
    } catch (e) {
        Toast.danger(e?.message);
    }
}
getEvaluationDimensionOption();
defineExpose({ resetFormData });
</script>

<style scoped lang="less">
.parentId {
    :deep(.b-form-item-content) {
        max-width: 50%;
    }
}
</style>
