import { downloadFile, get, post, postForm } from '../http';

/**
 * https://api.weizhipin.com/project/2353/interface/api/635231
 * 测评模板-模板参数保存
 */
export function _scoreParamSave(params: any) {
    return post('/wapi/admin/evaluation/score/param/save.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/635237
 * 测评模板-模板参数列表
 */
export function _scoreParamList(params: any) {
    return get('/wapi/admin/evaluation/score/param/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/678585
 * 测评模板-模板参数详情
 */
export function _scoreParamDetail(params: any) {
    return get('/wapi/admin/evaluation/score/param/detail.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/699518
 * 测评计分-参数excel导入-开始执行
 */
export function _importBegin(params: any) {
    return postForm('/wapi/admin/import/evaluation/score/start', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/699520
 * 测评计分-参数excel导入-进度
 */
export function _importProgress(params: any) {
    return get('/wapi/admin/import/evaluation/score/progress', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/699521
 * 测评计分-参数excel导入-错误下载
 */
export function _errorDataDownload(fileName = '表1：得分超越百分比的错误.xlsx', params: any) {
    return downloadFile('/wapi/admin/import/evaluation/score/error', fileName, params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/702312
 * 测评计分-岗位管理-岗位列表
 */
export function _getJobList(params: any) {
    return get('/wapi/admin/evaluation/score/param/job/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/635231
 * 测评计分-岗位管理-岗位状态变更
 */
export function _updateStatus(params: any) {
    return post('/wapi/admin/evaluation/score/param/job/updateStatus.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/702315
 * 测评计分-岗位管理-岗位保存
 */
export function _saveJobInfo(params: any) {
    return post('/wapi/admin/evaluation/score/param/job/save.json', params);
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/744955
 * 测评计分-职业锚管理列表
 */
export function _getCareerAnchorList(params: any) {
    return get('/wapi/admin/evaluation/score/param/careerAnchor/list.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/744961
 * 测评计分-职业锚管理列表-可选测评维度（级联）
 */
export function _getDimensionV2List(params: any) {
    return get('/wapi/admin/evaluation/dimension/v2/option.json', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/744958
 * 测评计分-职业锚管理列表-保存
 */
export function _saveCareerAnchorInfo(params: any) {
    return post('/wapi/admin/evaluation/score/param/careerAnchor/save.json', params);
}

/**
 * 企业定制模型-模型列表
 */
export function _getCorpModelList(params: any) {
    return get('/wapi/admin/evaluation/score/corp/model/list.json', params);
}

/**
 * 企业定制模型-状态切换
 */
export function _updateCorpModelStatus(params: any) {
    return post('/wapi/admin/evaluation/score/corp/model/updateStatus.json', params);
}
